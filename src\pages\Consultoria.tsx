import { useEffect } from "react";
import { Link } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { 
  CheckCircle, 
  Clock, 
  Users, 
  Star, 
  BookOpen, 
  Target,
  ArrowRight,
  Award,
  TrendingUp,
  Lightbulb,
  BarChart3,
  MessageSquare,
  Calendar
} from "lucide-react";

const Consultoria = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Consultoria Empresarial | ATAC Academy";
  }, []);

  const benefits = [
    {
      icon: <Target className="h-8 w-8 text-brand" />,
      title: "Estratégia Personalizada",
      description: "Desenvolvimento de estratégias sob medida para o seu negócio"
    },
    {
      icon: <TrendingUp className="h-8 w-8 text-brand" />,
      title: "Crescimento Acelerado",
      description: "Metodologias comprovadas para acelerar o crescimento da sua empresa"
    },
    {
      icon: <BarChart3 className="h-8 w-8 text-brand" />,
      title: "Análise de Performance",
      description: "Diagnóstico completo e métricas para otimizar resultados"
    },
    {
      icon: <Lightbulb className="h-8 w-8 text-brand" />,
      title: "Inovação Contínua",
      description: "Implementação de processos inovadores e melhores práticas"
    }
  ];

  const process = [
    {
      step: "01",
      title: "Diagnóstico Inicial",
      description: "Análise completa da situação atual da sua empresa"
    },
    {
      step: "02", 
      title: "Estratégia Customizada",
      description: "Desenvolvimento de plano estratégico personalizado"
    },
    {
      step: "03",
      title: "Implementação",
      description: "Execução das estratégias com acompanhamento contínuo"
    },
    {
      step: "04",
      title: "Resultados",
      description: "Monitoramento e otimização dos resultados obtidos"
    }
  ];

  const testimonials = [
    {
      name: "Carlos Silva",
      company: "TechStart Ltda",
      text: "A consultoria da Renata transformou completamente nossa empresa. Em 6 meses, aumentamos nossa receita em 150%.",
      rating: 5
    },
    {
      name: "Maria Santos",
      company: "Inovação Digital",
      text: "Metodologia excepcional e resultados concretos. Recomendo para qualquer empresa que busca crescimento.",
      rating: 5
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative pt-16 pb-20 overflow-hidden">
        <div className="absolute inset-0">
          <img 
            src="https://images.unsplash.com/photo-1573497701240-345a300b8d36?q=80&w=2940&auto=format&fit=crop"
            alt="Consultoria Empresarial"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black/60"></div>
        </div>
        
        <div className="relative container mx-auto px-4 pt-20">
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="inline-flex items-center bg-brand/20 backdrop-blur-sm rounded-full px-6 py-2 mb-6">
              <Award className="h-5 w-5 mr-2" />
              <span className="text-sm font-medium">Consultoria Premium</span>
            </div>
            
            <h1 className="font-montserrat text-4xl md:text-6xl font-bold mb-6">
              Consultoria <span className="text-brand-accent">Completa</span> para sua Empresa
            </h1>
            
            <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto">
              Transforme sua empresa com estratégias personalizadas e metodologias comprovadas. 
              2 meses de consultoria intensiva com resultados garantidos.
            </p>
            
            <div className="flex flex-wrap justify-center gap-6 mb-12">
              <div className="flex items-center bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2">
                <Clock className="h-5 w-5 mr-2 text-brand-accent" />
                <span>2 meses</span>
              </div>
              <div className="flex items-center bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2">
                <Users className="h-5 w-5 mr-2 text-brand-accent" />
                <span>64+ empresas atendidas</span>
              </div>
              <div className="flex items-center bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2">
                <Star className="h-5 w-5 mr-2 text-yellow-400 fill-yellow-400" />
                <span>4.9/5 de satisfação</span>
              </div>
              <div className="flex items-center bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2">
                <BookOpen className="h-5 w-5 mr-2 text-brand-accent" />
                <span>16 sessões + 8 workshops</span>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link to="/contato">
                <Button className="bg-brand hover:bg-brand-secondary text-white px-8 py-6 text-lg font-medium">
                  Solicitar Consultoria
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Button 
                variant="outline" 
                className="border-white text-white hover:bg-white hover:text-gray-900 px-8 py-6 text-lg font-medium"
              >
                <MessageSquare className="mr-2 h-5 w-5" />
                Falar com Especialista
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Por que escolher nossa <span className="text-brand">Consultoria?</span>
            </h2>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto">
              Metodologia comprovada que já transformou mais de 64 empresas em diversos segmentos
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <Card key={index} className="text-center p-6 hover:shadow-lg transition-shadow">
                <CardContent className="pt-6">
                  <div className="w-16 h-16 bg-brand/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    {benefit.icon}
                  </div>
                  <h3 className="font-semibold text-xl mb-3">{benefit.title}</h3>
                  <p className="text-gray-600">{benefit.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Instructor Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <img 
                  src="https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                  alt="Renata Souza"
                  className="w-full rounded-xl shadow-lg"
                  loading="lazy"
                />
              </div>
              <div>
                <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                  Conheça sua <span className="text-brand">Consultora</span>
                </h2>
                <h3 className="text-2xl font-semibold text-brand mb-4">Renata Souza</h3>
                <p className="text-lg text-gray-700 mb-6">
                  Especialista em estratégia empresarial com mais de 15 anos de experiência. 
                  MBA em Gestão Estratégica e certificações internacionais em consultoria empresarial.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <span>+200 empresas consultadas</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <span>Especialista em Scale-ups</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <span>Certificação Internacional</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Nossa <span className="text-brand">Metodologia</span>
            </h2>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto">
              Processo estruturado em 4 etapas para garantir resultados excepcionais
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {process.map((step, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-brand text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  {step.step}
                </div>
                <h3 className="font-semibold text-xl mb-3">{step.title}</h3>
                <p className="text-gray-600">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              O que nossos <span className="text-brand">Clientes</span> dizem
            </h2>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto">
              Resultados reais de empresas que transformaram seus negócios
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="p-6">
                <CardContent className="pt-6">
                  <div className="flex mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-yellow-400" />
                    ))}
                  </div>
                  <p className="text-gray-700 mb-4 italic">"{testimonial.text}"</p>
                  <div>
                    <p className="font-semibold">{testimonial.name}</p>
                    <p className="text-sm text-gray-600">{testimonial.company}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Perguntas <span className="text-brand">Frequentes</span>
            </h2>
          </div>

          <div className="max-w-3xl mx-auto space-y-6">
            <Card className="p-6">
              <CardContent className="pt-6">
                <h3 className="font-semibold text-lg mb-3">Como funciona a consultoria?</h3>
                <p className="text-gray-700">
                  Nossa consultoria é um programa intensivo de 2 meses com 16 sessões individuais e 8 workshops práticos.
                  Trabalhamos de forma híbrida, combinando encontros presenciais e online.
                </p>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardContent className="pt-6">
                <h3 className="font-semibold text-lg mb-3">Qual o investimento necessário?</h3>
                <p className="text-gray-700">
                  O investimento varia conforme o porte e necessidades da sua empresa.
                  Entre em contato para uma proposta personalizada e condições especiais.
                </p>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardContent className="pt-6">
                <h3 className="font-semibold text-lg mb-3">Que tipo de empresa pode participar?</h3>
                <p className="text-gray-700">
                  Atendemos empresas de todos os portes, desde startups até grandes corporações.
                  Nossa metodologia é adaptável para diferentes segmentos e estágios de maturidade.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-brand to-brand-accent text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="font-montserrat text-3xl md:text-4xl font-bold mb-6">
            Pronto para transformar sua empresa?
          </h2>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Agende uma conversa gratuita com nossa especialista e descubra como podemos acelerar o crescimento do seu negócio.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link to="/contato">
              <Button className="bg-white text-brand hover:bg-gray-100 px-8 py-6 text-lg font-medium">
                <Calendar className="mr-2 h-5 w-5" />
                Agendar Conversa Gratuita
              </Button>
            </Link>
            <Button
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-brand px-8 py-6 text-lg font-medium"
            >
              <MessageSquare className="mr-2 h-5 w-5" />
              Falar no WhatsApp
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Consultoria;
