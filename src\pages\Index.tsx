
import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";
import PurchaseAssistant from "@/components/PurchaseAssistant";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { 
  Clock, 
  Star, 
  BookOpen, 
  Rocket, 
  Globe, 
  Lightbulb, 
  Share2, 
  Target,
  BarChart2,
  Tag
} from "lucide-react";

const coursesByCategory = [
    {
      id: 13,
      title: "Consultoria",
      description: "Formação completa para desenvolvimento de habilidades e superação de desafios de carreira",
      image: "https://images.unsplash.com/photo-1573497701240-345a300b8d36?q=80&w=2940&auto=format&fit=crop",
      duration: "2 meses",
      students: 64,
      lessons: 16,
      workshops: 8,
      rating: 4.9,
      price: "R$ 5.997",
      priceAmount: 5997,
      instructor: "Renata Souza",
      type: "Híbrido",
      url: "/open-innovation",
      showPrice: false
    },
    {
      id: 14,
      title: "Executive Program",
      description: "Imersão presencial de 3 dias para executivos focada em liderança, inovação e transformação digital",
      image: "https://images.unsplash.com/photo-1542744173-05336fcc7ad4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80",
      duration: "3 dias",
      students: 74,
      lessons: 6,
      workshops: 4,
      rating: 4.8,
      price: "R$ 5.800",
      priceAmount: 5800,
      instructor: "Eduardo Santos",
      type: "Presencial",
      url: "/open-innovation",
      showPrice: false
    }
]

const sortCoursesByPrice = (courses) => {
  return [...courses].sort((a, b) => a.priceAmount - b.priceAmount);
};


const CourseCard = ({ course }) => {
  const getUrl = (course) => {
    return course.url;
  };

  return (
    <Card className="overflow-hidden border border-gray-100 shadow-md hover:shadow-lg transition-all duration-300 group hover:-translate-y-1">
      <div className="relative h-52 overflow-hidden">
        <img 
          src={course.image} 
          alt={course.title}
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
        />
        <div className="absolute top-4 right-4 bg-brand-accent text-white text-sm font-medium py-1 px-3 rounded-full">
          {course.type}
        </div>
        {course.hasPromotion && (
          <div className="absolute top-4 left-4 bg-red-500 text-white text-sm font-medium py-1 px-3 rounded-full flex items-center">
            <Tag className="h-3 w-3 mr-1" />
            Promoção
          </div>
        )}
      </div>
      <CardHeader className="p-6 pb-2">
        <h3 className="font-montserrat text-xl font-semibold text-gray-900 group-hover:text-brand transition-colors">
          {course.title}
        </h3>
      </CardHeader>
      <CardContent className="p-6 pt-2 pb-4">
        <p className="text-gray-700 mb-4 line-clamp-2">{course.description}</p>
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center">
            <Clock className="h-4 w-4 text-brand-accent mr-2" />
            <span className="text-sm">{course.duration}</span>
          </div>
          <div className="flex items-center">
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500 mr-2" />
            <span className="text-sm">{course.rating}</span>
          </div>
          {course.lessons > 0 && (
            <div className="flex items-center">
              <BookOpen className="h-4 w-4 text-brand mr-2" />
              <span className="text-sm">{course.lessons} aulas</span>
            </div>
          )}
          <div className="flex items-center">
            <span className="text-sm">{course.workshops} workshops</span>
          </div>
        </div>
       
      </CardContent>
      <CardFooter className="p-6 pt-0">
        <Link to={"#"} className="w-full">
          <Button className="w-full bg-brand hover:bg-brand-secondary">
            Saiba Mais
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
};

const CategoryHeader = ({ icon, title, description }) => {
  return (
    <div className="mb-8">
      <div className="flex items-center mb-3">
        {icon}
        <h2 className="font-montserrat text-2xl md:text-3xl font-bold text-gray-900 ml-3">
          {title}
        </h2>
      </div>
      <p className="text-lg text-gray-700">
        {description}
      </p>
    </div>
  );
};

const Cursos = () => {
  const [activeTab, setActiveTab] = useState("all");
  
  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Programas de Aceleração | ATAC Academy";
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <Hero 
        smallText="PROGRAMAS DE ACELERAÇÃO"
        title="Programas de <span>Aceleração</span> e <span class='text-brand-accent'>Capacitação</span>"
        description="Impulsione sua jornada empreendedora com nossos programas de longa duração e conteúdos especializados para diferentes estágios do seu negócio."
        primaryButtonText="Ver Todos os Programas"
        primaryButtonLink="#programas"
        secondaryButtonText="Fale com um Consultor"
        secondaryButtonLink="/contato"
        imageUrl="https://images.unsplash.com/photo-1542744173-8e7e53415bb0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
        showCarousel={false}
        stats={[
          { text: "+500 Alunos Formados", icon: Target },
          { text: "97% de Satisfação", icon: Star }
        ]}
      />
      <main className="py-20" id="programas">
        <div className="container mx-auto px-4">          
          <div className="mb-12">
              <CategoryHeader 
                icon={<div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center"><Lightbulb className="h-5 w-5 text-brand" /></div>}
                title="Cursos e Workshops" 
                description="Programas focados em capacitação para empreendedores que desejam iniciar ou alavancar seus negócios"
              />
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {coursesByCategory.map(course => (
                  <CourseCard key={course.id} course={course} />
                ))}
              </div>
            
          </div>
        </div>
      </main>
      
      {/* Add Purchase Assistant */}
      <PurchaseAssistant productName="Cursos e Programas" productType="Online e Presencial" />
      
      <Footer />
    </div>
  );
};

export default Cursos;
